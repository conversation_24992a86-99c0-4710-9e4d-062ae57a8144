// dartboard.js

const svgNS = "http://www.w3.org/2000/svg";

// Standard dartboard numbers order (clockwise from top)
const boardNumbers = [20, 1, 18, 4, 13, 6, 10, 15, 2, 17, 3, 19, 7, 16, 8, 11, 14, 9, 12, 5];

// Store callback for when a segment is clicked
let onSegmentClickCallback = null;

// Basic representation of dartboard segments and their values
const segmentValues = {
    // Singles 1-20
    ...Object.fromEntries(Array.from({ length: 20 }, (_, i) => [`s${i + 1}`, { score: i + 1, multiplier: 1, text: `S${i+1}` }])),
    // Doubles 1-20
    ...Object.fromEntries(Array.from({ length: 20 }, (_, i) => [`d${i + 1}`, { score: i + 1, multiplier: 2, text: `D${i+1}` }])),
    // Triples 1-20
    ...Object.fromEntries(Array.from({ length: 20 }, (_, i) => [`t${i + 1}`, { score: i + 1, multiplier: 3, text: `T${i+1}` }])),
    // Bullseye
    'bull': { score: 25, multiplier: 1, text: 'Bull' }, // Single Bull
    'dbull': { score: 25, multiplier: 2, text: 'DBull' }  // Double Bull (counts as 50)
};

// Function to create a wedge path for the dartboard
function createWedge(angleStart, angleEnd, innerRadius, outerRadius, centerX, centerY, segmentId, segmentClass) {
    const path = document.createElementNS(svgNS, "path");
    const startXOuter = centerX + outerRadius * Math.cos(angleStart);
    const startYOuter = centerY + outerRadius * Math.sin(angleStart);
    const endXOuter = centerX + outerRadius * Math.cos(angleEnd);
    const endYOuter = centerY + outerRadius * Math.sin(angleEnd);
    const startXInner = centerX + innerRadius * Math.cos(angleStart);
    const startYInner = centerY + innerRadius * Math.sin(angleStart);
    const endXInner = centerX + innerRadius * Math.cos(angleEnd);
    const endYInner = centerY + innerRadius * Math.sin(angleEnd);

    const largeArcFlag = (angleEnd - angleStart) <= Math.PI ? "0" : "1";

    const d = [
        `M ${startXInner} ${startYInner}`, // Move to inner start
        `L ${startXOuter} ${startYOuter}`, // Line to outer start
        `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${endXOuter} ${endYOuter}`, // Arc to outer end
        `L ${endXInner} ${endYInner}`, // Line to inner end
        `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${startXInner} ${startYInner}`, // Arc back to inner start
        "Z"
    ].join(" ");

    path.setAttribute("d", d);
    path.setAttribute("id", segmentId);
    path.setAttribute("class", `dartboard-segment ${segmentClass}`);
    path.addEventListener('click', () => handleSegmentClick(segmentId));
    return path;
}

function createCircleSegment(cx, cy, r, segmentId, segmentClass) {
    const circle = document.createElementNS(svgNS, "circle");
    circle.setAttribute("cx", cx);
    circle.setAttribute("cy", cy);
    circle.setAttribute("r", r);
    circle.setAttribute("id", segmentId);
    circle.setAttribute("class", `dartboard-segment ${segmentClass}`);
    circle.addEventListener('click', () => handleSegmentClick(segmentId));
    return circle;
}


export function initDartboard(segmentClickCallback) {
    onSegmentClickCallback = segmentClickCallback;
    const svg = document.getElementById('interactive-dartboard');
    if (!svg) {
        console.error("Dartboard SVG element not found");
        return;
    }

    svg.innerHTML = ''; // Clear previous if any

    const centerX = 300, centerY = 300;
    const R = 200; // Outer radius of scoring area

    // Define radii for different bands
    const radii = {
        doubleBull: R * 0.035,      // Double Bull (50)
        singleBull: R * 0.08,       // Single Bull (25)
        innerSingle: R * 0.45,      // Inner band of single numbers
        tripleRingInner: R * 0.50,  // Inner edge of triple ring
        tripleRingOuter: R * 0.58,  // Outer edge of triple ring
        outerSingle: R * 0.90,      // Outer band of single numbers (inner edge of double ring)
        doubleRingInner: R * 0.92,  // Inner edge of double ring
        doubleRingOuter: R * 1.00   // Outer edge of double ring (board edge)
    };
    
    const angleStep = (2 * Math.PI) / 20; // 18 degrees per segment

    // Add a "Miss" area as the background (dark green like a real dartboard)
    const missArea = createCircleSegment(centerX, centerY, R * 1.6, 'miss', 'segment-miss');
    missArea.setAttribute('fill', '#1F4E3D'); // Dark green background
    svg.appendChild(missArea); // Add as background

    // Add dartboard wire frame (the metal separators)
    const wireFrame = document.createElementNS(svgNS, "circle");
    wireFrame.setAttribute("cx", centerX);
    wireFrame.setAttribute("cy", centerY);
    wireFrame.setAttribute("r", R);
    wireFrame.setAttribute("fill", "none");
    wireFrame.setAttribute("stroke", "#C0C0C0"); // Silver wire
    wireFrame.setAttribute("stroke-width", "2");
    svg.appendChild(wireFrame);

    // Create segments
    for (let i = 0; i < 20; i++) {
        const number = boardNumbers[i];
        // Angle for the center of the segment. Standard dartboard has 20 at the top.
        // SVG angles are 0 = right, PI/2 = down. Start 20 at -PI/2 (top).
        // Each segment is 18deg. Midpoint of first segment (20) is at -90deg. Start of first segment is -90 - 9 = -99deg.
        const baseAngle = -Math.PI / 2 - angleStep / 2; // Start so 20 is centered at top
        const angleStart = baseAngle + i * angleStep;
        const angleEnd = baseAngle + (i + 1) * angleStep;

        // Determine if this is a black or cream segment (traditional dartboard pattern)
        // In a real dartboard, segments alternate black and cream/white
        // Black segments: 6, 13, 4, 18, 1, 20, 5, 12, 9, 14, 11, 8, 16, 7, 19, 3, 17, 2, 15, 10
        // But let's use a simpler alternating pattern based on position
        const isBlackSegment = i % 2 === 1; // Alternate every other segment
        const singleClass = isBlackSegment ? 'segment-single-odd' : 'segment-single';

        // Outer Double Ring
        svg.appendChild(createWedge(angleStart, angleEnd, radii.doubleRingInner, radii.doubleRingOuter, centerX, centerY, `d${number}`, 'segment-double'));
        // Outer Single Band
        svg.appendChild(createWedge(angleStart, angleEnd, radii.tripleRingOuter, radii.doubleRingInner, centerX, centerY, `s${number}_outer`, singleClass));
        // Triple Ring
        svg.appendChild(createWedge(angleStart, angleEnd, radii.tripleRingInner, radii.tripleRingOuter, centerX, centerY, `t${number}`, 'segment-triple'));
        // Inner Single Band
        svg.appendChild(createWedge(angleStart, angleEnd, radii.singleBull, radii.tripleRingInner, centerX, centerY, `s${number}`, singleClass));
    
        // Add number text with background circle for visibility
        const textAngle = angleStart + angleStep / 2;
        const textRadius = R * 1.35; // Position numbers well outside the board but within the miss area
        const textX = centerX + textRadius * Math.cos(textAngle);
        const textY = centerY + textRadius * Math.sin(textAngle) + 8; // +8 for text baseline adjustment

        // Add white background circle for number
        const numberBg = document.createElementNS(svgNS, "circle");
        numberBg.setAttribute("cx", textX);
        numberBg.setAttribute("cy", textY - 4); // Adjust for text baseline
        numberBg.setAttribute("r", "16");
        numberBg.setAttribute("fill", "#FFFFFF");
        numberBg.setAttribute("stroke", "#000000");
        numberBg.setAttribute("stroke-width", "2");
        numberBg.setAttribute("pointer-events", "none"); // Don't interfere with clicks
        svg.appendChild(numberBg);

        const textElement = document.createElementNS(svgNS, "text");
        textElement.setAttribute("x", textX);
        textElement.setAttribute("y", textY);
        textElement.setAttribute("text-anchor", "middle");
        textElement.setAttribute("dominant-baseline", "middle");
        textElement.setAttribute("font-size", "20");
        textElement.setAttribute("font-weight", "bold");
        textElement.setAttribute("font-family", "Arial, sans-serif");
        textElement.setAttribute("fill", "#000000"); // Black text on white background
        textElement.setAttribute("class", "dartboard-number");
        textElement.setAttribute("pointer-events", "none"); // Don't interfere with clicks
        textElement.textContent = number;
        svg.appendChild(textElement);
    }

    // Add radial wire separators between segments
    for (let i = 0; i < 20; i++) {
        const angle = -Math.PI / 2 - angleStep / 2 + i * angleStep;
        const startX = centerX + radii.doubleBull * Math.cos(angle);
        const startY = centerY + radii.doubleBull * Math.sin(angle);
        const endX = centerX + radii.doubleRingOuter * Math.cos(angle);
        const endY = centerY + radii.doubleRingOuter * Math.sin(angle);

        const wireLine = document.createElementNS(svgNS, "line");
        wireLine.setAttribute("x1", startX);
        wireLine.setAttribute("y1", startY);
        wireLine.setAttribute("x2", endX);
        wireLine.setAttribute("y2", endY);
        wireLine.setAttribute("stroke", "#C0C0C0");
        wireLine.setAttribute("stroke-width", "1");
        wireLine.setAttribute("pointer-events", "none");
        svg.appendChild(wireLine);
    }

    // Add wire frames for scoring rings
    const wireRings = [
        { radius: radii.singleBull, width: 1 },
        { radius: radii.tripleRingInner, width: 1 },
        { radius: radii.tripleRingOuter, width: 1 },
        { radius: radii.doubleRingInner, width: 1 },
        { radius: radii.doubleRingOuter, width: 2 }
    ];

    wireRings.forEach(ring => {
        const wireCircle = document.createElementNS(svgNS, "circle");
        wireCircle.setAttribute("cx", centerX);
        wireCircle.setAttribute("cy", centerY);
        wireCircle.setAttribute("r", ring.radius);
        wireCircle.setAttribute("fill", "none");
        wireCircle.setAttribute("stroke", "#C0C0C0");
        wireCircle.setAttribute("stroke-width", ring.width);
        wireCircle.setAttribute("pointer-events", "none"); // Don't interfere with clicks
        svg.appendChild(wireCircle);
    });

    // Bullseye
    svg.appendChild(createCircleSegment(centerX, centerY, radii.singleBull, 'bull', 'segment-bull')); // Single Bull (25)
    svg.appendChild(createCircleSegment(centerX, centerY, radii.doubleBull, 'dbull', 'segment-double-bull')); // Double Bull (50)

    // Dartboard initialization complete
}


function handleSegmentClick(segmentId) {
    if (segmentId === 'miss') {
        if (onSegmentClickCallback) onSegmentClickCallback({ score: 0, multiplier: 0, text: 'Miss', value: 0 });
        return;
    }

    const dart = segmentValues[segmentId];
    if (dart && onSegmentClickCallback) {
        const dartWithValue = {...dart, value: getDartValue(dart)};
        onSegmentClickCallback(dartWithValue);
    } else if (onSegmentClickCallback) {
        // Try parsing as S, D, T + number if ID is just number, or handle outer single segments
        if (segmentId.includes('_outer')) {
            // Handle outer single segments like "s20_outer"
            const numMatch = segmentId.match(/s(\d+)_outer/);
            if (numMatch) {
                const score = parseInt(numMatch[1]);
                const dart = segmentValues[`s${score}`];
                if (dart) {
                    onSegmentClickCallback({...dart, value: getDartValue(dart)});
                    return;
                }
            }
        }

        const numMatch = segmentId.match(/\d+/);
        if (numMatch) {
            const score = parseInt(numMatch[0]);
            if (segmentId.startsWith('d')) {
                const dart = segmentValues[`d${score}`];
                onSegmentClickCallback({...dart, value: getDartValue(dart)});
            }
            else if (segmentId.startsWith('t')) {
                const dart = segmentValues[`t${score}`];
                onSegmentClickCallback({...dart, value: getDartValue(dart)});
            }
            else {
                const dart = segmentValues[`s${score}`];
                onSegmentClickCallback({...dart, value: getDartValue(dart)});
            }
        } else {
            console.warn("Unknown segment clicked:", segmentId);
        }
    }
}


export function parseDart(inputText) {
    if (!inputText) return null;
    const text = inputText.toLowerCase().trim();

    // Direct matches for bullseye
    if (text === 'bull' || text === 'sb' || text === 'single bull' || text === '25') return segmentValues['bull'];
    if (text === 'dbull' || text === 'db' || text === 'double bull' || text === '50') return segmentValues['dbull'];
    if (text === 'miss' || text === '0' || text === 'm') return { score: 0, multiplier: 0, text: 'Miss', value: 0 };

    // Regex for patterns like T20, D5, S19, 20, 3x15, 2*10
    // Optional multiplier (t,d,s, 3x, 2x, 1x) followed by score number (1-20)
    const match = text.match(/^(t|d|s|triple|double|single|3x|2x|1x)?\s*([1-9]|1\d|20)$/);
    if (match) {
        let multiplierPrefix = match[1];
        let score = parseInt(match[2]);
        let multiplier = 1; // Default to single

        if (multiplierPrefix) {
            if (['t', 'triple', '3x'].includes(multiplierPrefix)) multiplier = 3;
            else if (['d', 'double', '2x'].includes(multiplierPrefix)) multiplier = 2;
            // s, single, 1x are already multiplier 1
        }
        
        const segmentId = `${multiplier === 3 ? 't' : multiplier === 2 ? 'd' : 's'}${score}`;
        if (segmentValues[segmentId]) {
            return {...segmentValues[segmentId], value: getDartValue(segmentValues[segmentId])};
        }
    }
    return null; // Invalid input
}

export function getDartValue(dart) { // dart = { score, multiplier } or { value }
    if (typeof dart.value !== 'undefined') return dart.value;
    if (dart.score === 25 && dart.multiplier === 2) return 50; // Double bull
    return dart.score * dart.multiplier;
}

// Initial call to draw the board - this might be better called from renderer.js after DOM is ready.
// For now, it's here. The callback will be set by renderer.js.
// initDartboard((segment) => console.log("Dartboard clicked (initial test):", segment)); // Test
