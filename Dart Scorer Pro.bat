@echo off
REM Dart Scorer Pro - Windows Launcher
REM This batch file launches the Dart Scorer Pro Electron application

REM Set the title of the command window
title Dart Scorer Pro - Starting...

REM Get the directory where this batch file is located
set "APP_DIR=%~dp0"

REM Change to the application directory
cd /d "%APP_DIR%"

REM Check if node_modules exists
if not exist "node_modules" (
    echo.
    echo ========================================
    echo  Dart Scorer Pro - First Time Setup
    echo ========================================
    echo.
    echo Installing dependencies...
    echo This may take a few minutes on first run.
    echo.
    
    REM Install dependencies
    call npm install
    
    if errorlevel 1 (
        echo.
        echo ERROR: Failed to install dependencies.
        echo Please make sure Node.js and npm are installed.
        echo.
        pause
        exit /b 1
    )
    
    echo.
    echo Dependencies installed successfully!
    echo.
)

REM Check if package.json exists
if not exist "package.json" (
    echo.
    echo ERROR: package.json not found!
    echo Please make sure this batch file is in the Dart Scorer Pro directory.
    echo.
    pause
    exit /b 1
)

REM Start the application
echo.
echo ========================================
echo  Starting Dart Scorer Pro...
echo ========================================
echo.

REM Hide the command window after starting (optional - remove REM to enable)
REM if not "%1"=="ELEV" (powershell "Start-Process '%0' -ArgumentList 'ELEV' -WindowStyle Hidden" & exit)

REM Launch the Electron application
call npm start

REM If we get here, the application has closed
echo.
echo Dart Scorer Pro has closed.
echo.
pause
