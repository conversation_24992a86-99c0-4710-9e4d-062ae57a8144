{"name": "electron-dart-scorer", "version": "1.0.0", "description": "A desktop dart scoring application for X01 games.", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "dist": "npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["darts", "scoring", "electron", "x01"], "author": "AI Assistant", "license": "ISC", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.13.3"}, "dependencies": {"firebase": "^10.12.2"}, "build": {"appId": "com.dartscorer.app", "productName": "Dart Scorer Pro", "directories": {"output": "dist"}, "files": ["main.js", "renderer.js", "index.html", "*.js", "*.css", "package.json", "!node_modules/**/*"], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}