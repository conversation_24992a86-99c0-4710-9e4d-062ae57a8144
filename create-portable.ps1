# Create Portable Distribution of Dart Scorer Pro
# This PowerShell script creates a minimal portable version

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  Creating Dart Scorer Pro Portable" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Set distribution directory
$DistDir = "Dart Scorer Pro Portable"

# Remove existing distribution if it exists
if (Test-Path $DistDir) {
    Write-Host "Removing existing distribution directory..." -ForegroundColor Yellow
    Remove-Item $DistDir -Recurse -Force
}

# Create distribution directory
Write-Host "Creating distribution directory..." -ForegroundColor Cyan
New-Item -ItemType Directory -Path $DistDir | Out-Null

# Copy essential application files
Write-Host "Copying application files..." -ForegroundColor Cyan
$AppFiles = @(
    "main.js",
    "renderer.js", 
    "index.html",
    "package.json"
)

foreach ($file in $AppFiles) {
    if (Test-Path $file) {
        Copy-Item $file $DistDir
        Write-Host "  ✓ $file" -ForegroundColor Gray
    } else {
        Write-Host "  ✗ $file (not found)" -ForegroundColor Red
    }
}

# Copy all JavaScript modules
Write-Host "Copying JavaScript modules..." -ForegroundColor Cyan
$JsFiles = Get-ChildItem -Filter "*.js" | Where-Object { $_.Name -notin $AppFiles }
foreach ($file in $JsFiles) {
    Copy-Item $file.FullName $DistDir
    Write-Host "  ✓ $($file.Name)" -ForegroundColor Gray
}

# Copy launcher files
Write-Host "Copying launcher files..." -ForegroundColor Cyan
$LauncherFiles = @(
    "Dart Scorer Pro.bat",
    "Dart Scorer Pro.vbs", 
    "Launch Dart Scorer Pro.cmd"
)

foreach ($file in $LauncherFiles) {
    if (Test-Path $file) {
        Copy-Item $file $DistDir
        Write-Host "  ✓ $file" -ForegroundColor Gray
    }
}

# Copy documentation
Write-Host "Copying documentation..." -ForegroundColor Cyan
$DocFiles = Get-ChildItem -Filter "*.md"
foreach ($file in $DocFiles) {
    Copy-Item $file.FullName $DistDir
    Write-Host "  ✓ $($file.Name)" -ForegroundColor Gray
}

# Create minimal node_modules with only Electron
Write-Host "Creating minimal node_modules..." -ForegroundColor Cyan
$NodeModulesDir = Join-Path $DistDir "node_modules"
New-Item -ItemType Directory -Path $NodeModulesDir | Out-Null

# Check if electron exists and copy it
$ElectronPath = "node_modules\electron"
if (Test-Path $ElectronPath) {
    Write-Host "Copying Electron runtime..." -ForegroundColor Cyan
    $ElectronDestPath = Join-Path $NodeModulesDir "electron"
    Copy-Item $ElectronPath $ElectronDestPath -Recurse
    Write-Host "  ✓ Electron runtime copied" -ForegroundColor Gray
} else {
    Write-Host "  ✗ ERROR: Electron not found in node_modules" -ForegroundColor Red
    Write-Host "  Please run 'npm install' first" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Create portable README
Write-Host "Creating portable README..." -ForegroundColor Cyan
$ReadmeContent = @"
Dart Scorer Pro - Portable Version

This is a portable version of Dart Scorer Pro that includes only the essential files needed to run the application.

TO RUN THE APPLICATION:
1. Double-click "Dart Scorer Pro.bat" (shows console window)
2. Double-click "Dart Scorer Pro.vbs" (runs silently)  
3. Double-click "Launch Dart Scorer Pro.cmd" (minimal console)

REQUIREMENTS:
- Node.js must be installed on the target computer
- Download from: https://nodejs.org/
- Choose the LTS (Long Term Support) version

FEATURES:
- Complete offline dart scoring functionality
- Save/load games locally
- Export/import game data
- Professional dartboard interface
- Multiple game modes (101, 201, 301, 501, 701)

PORTABLE BENEFITS:
- Much smaller than full development version
- No installation required (just extract and run)
- Includes only necessary files
- Works on any Windows computer with Node.js

SIZE COMPARISON:
- Full development folder: 150+ MB
- This portable version: ~40-50 MB
- Actual app code: ~2 MB

For support or updates, visit the original development folder.
"@

$ReadmeContent | Out-File -FilePath (Join-Path $DistDir "README-Portable.txt") -Encoding UTF8

# Calculate folder size
Write-Host ""
Write-Host "Calculating folder size..." -ForegroundColor Cyan
$FolderSize = (Get-ChildItem $DistDir -Recurse | Measure-Object -Property Length -Sum).Sum
$SizeMB = [math]::Round($FolderSize / 1MB, 2)
$SizeKB = [math]::Round($FolderSize / 1KB, 0)

# Display results
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  Portable Version Created Successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Location: $DistDir\" -ForegroundColor White
Write-Host "Size: $SizeMB MB ($SizeKB KB)" -ForegroundColor White
Write-Host ""
Write-Host "You can now:" -ForegroundColor Yellow
Write-Host "1. Copy the '$DistDir' folder to any computer with Node.js" -ForegroundColor Gray
Write-Host "2. Run any of the launcher files to start the application" -ForegroundColor Gray  
Write-Host "3. Zip the folder for easy distribution" -ForegroundColor Gray
Write-Host ""
Write-Host "Note: This portable version is much smaller than the full" -ForegroundColor Cyan
Write-Host "development folder because it excludes unnecessary files." -ForegroundColor Cyan
Write-Host ""

# Offer to create ZIP file
$CreateZip = Read-Host "Would you like to create a ZIP file for distribution? (y/n)"
if ($CreateZip -eq 'y' -or $CreateZip -eq 'Y') {
    $ZipName = "Dart-Scorer-Pro-Portable-v1.0.zip"
    Write-Host ""
    Write-Host "Creating ZIP file..." -ForegroundColor Cyan
    
    try {
        Compress-Archive -Path $DistDir -DestinationPath $ZipName -Force
        $ZipSize = [math]::Round((Get-Item $ZipName).Length / 1MB, 2)
        Write-Host "✓ ZIP created: $ZipName ($ZipSize MB)" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to create ZIP: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Read-Host "Press Enter to exit"
