# Dart Scorer Pro - Tauri Migration Guide

Tauri is a modern alternative to Electron that creates much smaller desktop applications.

## 📊 Size Comparison

| Framework | Runtime Size | Final App Size | Memory Usage |
|-----------|-------------|----------------|--------------|
| **Electron** | 252 MB | 300+ MB | 100+ MB RAM |
| **Tauri** | 15 MB | 20-30 MB | 30-50 MB RAM |
| **Web Version** | 0 MB | 2 MB | Browser dependent |

## 🚀 Tauri Benefits

### Size Advantages:
- **90% smaller** than Electron
- Uses system webview instead of bundling Chromium
- Rust backend is highly optimized
- No Node.js runtime needed

### Performance Benefits:
- **Faster startup** (no Chromium initialization)
- **Lower memory usage** (uses system resources)
- **Better security** (Rust memory safety)
- **Native performance** for backend operations

## 🔧 Migration Steps

### 1. Install Tauri Prerequisites
```bash
# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Install Node.js (for frontend build)
# Download from https://nodejs.org/

# Install Tauri CLI
npm install -g @tauri-apps/cli
```

### 2. Initialize Tauri Project
```bash
# In your project directory
npm install @tauri-apps/api
tauri init
```

### 3. Configure Tauri
Create `src-tauri/tauri.conf.json`:
```json
{
  "build": {
    "beforeBuildCommand": "",
    "beforeDevCommand": "",
    "devPath": "../",
    "distDir": "../"
  },
  "package": {
    "productName": "Dart Scorer Pro",
    "version": "1.0.0"
  },
  "tauri": {
    "allowlist": {
      "all": false,
      "fs": {
        "all": true,
        "scope": ["$APPDATA/dart-scorer/*"]
      },
      "dialog": {
        "all": true
      }
    },
    "bundle": {
      "active": true,
      "targets": "all",
      "identifier": "com.dartscorer.app",
      "icon": [
        "icons/32x32.png",
        "icons/128x128.png",
        "icons/icon.icns",
        "icons/icon.ico"
      ]
    },
    "security": {
      "csp": null
    },
    "updater": {
      "active": false
    },
    "windows": [
      {
        "fullscreen": false,
        "height": 800,
        "resizable": true,
        "title": "Dart Scorer Pro",
        "width": 1200,
        "minWidth": 800,
        "minHeight": 600
      }
    ]
  }
}
```

### 4. Update Package.json
```json
{
  "scripts": {
    "tauri": "tauri",
    "tauri-dev": "tauri dev",
    "tauri-build": "tauri build"
  },
  "devDependencies": {
    "@tauri-apps/cli": "^1.5.0"
  },
  "dependencies": {
    "@tauri-apps/api": "^1.5.0"
  }
}
```

### 5. Replace Electron APIs
Replace Electron-specific code with Tauri APIs:

```javascript
// OLD (Electron)
const { ipcRenderer } = require('electron');

// NEW (Tauri)
import { invoke } from '@tauri-apps/api/tauri';
import { save, open } from '@tauri-apps/api/dialog';
import { writeTextFile, readTextFile } from '@tauri-apps/api/fs';
```

### 6. Build Commands
```bash
# Development
npm run tauri-dev

# Production build
npm run tauri-build
```

## 📁 File Structure After Migration

```
dart-scorer-pro/
├── src/                    # Frontend files (your current JS/HTML/CSS)
│   ├── index.html
│   ├── renderer.js
│   ├── *.js
│   └── assets/
├── src-tauri/              # Tauri backend (Rust)
│   ├── src/
│   │   └── main.rs
│   ├── Cargo.toml
│   ├── tauri.conf.json
│   └── icons/
├── package.json
└── README.md
```

## 🔄 Code Changes Required

### Storage Operations
```javascript
// OLD (Electron + localStorage)
localStorage.setItem('games', JSON.stringify(games));

// NEW (Tauri file system)
import { writeTextFile } from '@tauri-apps/api/fs';
await writeTextFile('games.json', JSON.stringify(games), { dir: 'appData' });
```

### File Dialogs
```javascript
// OLD (Electron)
const { dialog } = require('electron').remote;

// NEW (Tauri)
import { save, open } from '@tauri-apps/api/dialog';
const filePath = await save({
  filters: [{
    name: 'JSON',
    extensions: ['json']
  }]
});
```

## 🎯 Migration Effort

### Easy (No changes needed):
- HTML/CSS structure
- Game logic (dartboard.js, game.js, etc.)
- UI components
- Styling

### Medium (API replacements):
- File operations
- Storage management
- Dialog boxes

### Hard (Complete rewrite):
- Main process (Rust instead of Node.js)
- Build configuration
- Distribution setup

## 📦 Final Results

After Tauri migration:
- **App size**: 20-30 MB (vs 300+ MB Electron)
- **Memory usage**: 30-50 MB (vs 100+ MB Electron)
- **Startup time**: 2-3 seconds (vs 5-10 seconds Electron)
- **Distribution**: Single executable file

## 🚀 Quick Start Alternative

If migration seems complex, consider these immediate solutions:

### 1. Web Version (Easiest)
```bash
# Run the web version script
./create-web-version.ps1
# Result: 2 MB web application
```

### 2. Electron Builder Optimization
```bash
# Use optimized Electron build
npm run build-win
# Result: 60 MB installer (vs 252 MB development)
```

### 3. Progressive Web App (PWA)
- Add service worker for offline functionality
- Enable "Add to Desktop" in browsers
- Size: ~2 MB with full offline capability

## 🎯 Recommendation

For **immediate size reduction**:
1. Use the web version (2 MB)
2. Or use Electron Builder (60 MB installer)

For **long-term optimization**:
1. Migrate to Tauri (20-30 MB final app)
2. Or create PWA version (2 MB + offline)

The web version gives you 99% size reduction with minimal effort!
