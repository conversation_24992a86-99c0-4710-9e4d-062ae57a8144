DART SCORER PRO - ESSEN<PERSON><PERSON> FILES FOR DISTRIBUTION
==================================================

If you want to manually create a smaller distribution package, 
include ONLY these files (total size: ~40-50 MB vs 150+ MB full folder):

CORE APPLICATION FILES (Required):
├── main.js                          // Electron main process
├── renderer.js                      // Main application logic  
├── index.html                       // User interface
├── package.json                     // App configuration

GAME LOGIC MODULES (Required):
├── dartboard.js                     // Interactive dartboard
├── game.js                          // Game state management
├── player.js                        // Player management
├── ui.js                            // User interface functions
├── checkout.js                      // Checkout calculations
├── storage.js                       // Firebase storage (optional)
├── offline-storage.js               // Local storage system

LAUNCHER FILES (Windows - Optional but Recommended):
├── Dart Scorer Pro.bat              // Detailed launcher with console
├── Dart Scorer Pro.vbs              // Silent launcher (no console)
├── Launch Dart Scorer Pro.cmd       // Quick launcher

DOCUMENTATION (Optional):
├── README.md                        // Basic usage instructions
├── Offline Storage Guide.md         // Offline storage documentation
├── Build and Distribution Guide.md  // This guide
├── Windows Launcher Instructions.md // Launcher documentation

RUNTIME (Required):
└── node_modules/
    └── electron/                    // ONLY the electron folder
        ├── dist/                    // Electron binaries
        ├── package.json             // Electron package info
        └── (other electron files)   // Electron runtime files

EXCLUDE THESE (Save 100+ MB):
├── node_modules/firebase/           // Large Firebase SDK (if using offline only)
├── node_modules/@firebase/          // Firebase dependencies
├── node_modules/undici/             // HTTP client
├── node_modules/ws/                 // WebSocket library
├── node_modules/protobufjs/         // Protocol buffers
├── node_modules/long/               // Long integer library
├── node_modules/faye-websocket/     // WebSocket implementation
└── (all other node_modules folders) // Development dependencies

MANUAL DISTRIBUTION STEPS:
==========================

1. Create new folder: "Dart Scorer Pro Portable"

2. Copy CORE APPLICATION FILES to the folder

3. Copy GAME LOGIC MODULES to the folder

4. Copy LAUNCHER FILES to the folder (for Windows users)

5. Create node_modules folder in the portable folder

6. Copy ONLY node_modules/electron/ to portable/node_modules/

7. Optionally copy documentation files

8. Create ZIP file of the portable folder

RESULT:
=======
- Original development folder: 150+ MB (thousands of files)
- Portable distribution: 40-50 MB (essential files only)
- Compression ratio: ~70% smaller
- Functionality: 100% identical

AUTOMATED ALTERNATIVES:
======================
Instead of manual copying, use these automated methods:

1. ELECTRON BUILDER (Professional):
   npm install
   npm run build-win
   Result: Professional installer (~60 MB)

2. PORTABLE SCRIPT (Simple):
   Run: create-portable.bat
   Result: Portable folder (~40 MB)

3. POWERSHELL SCRIPT (Advanced):
   Run: create-portable.ps1
   Result: Portable folder + optional ZIP

DISTRIBUTION RECOMMENDATIONS:
============================

FOR END USERS:
- Use Electron Builder for professional installers
- Creates proper Windows/Mac/Linux packages
- Includes auto-updater capability
- Best user experience

FOR QUICK SHARING:
- Use portable scripts for simple distribution
- No installation required
- Works immediately after extraction
- Good for testing or temporary use

FOR WEB DEPLOYMENT:
- Remove Electron dependency entirely
- Host as web application
- Smallest possible size (~2 MB)
- Universal compatibility

The portable version maintains 100% functionality while being 
significantly smaller and easier to distribute!
