# Dart Scorer Pro - Windows Launcher Instructions

This folder contains several launcher files that allow you to run Dart Scorer Pro on Windows without using the terminal/command prompt.

## 📁 Available Launcher Files

### 1. `Dart Scorer Pro.bat` (Recommended for beginners)
- **Best for**: First-time users who want to see what's happening
- **Features**: 
  - Shows detailed startup process
  - Automatically installs dependencies on first run
  - Displays helpful error messages
  - Keeps console window open for troubleshooting
- **How to use**: Double-click the file

### 2. `Dart Scorer Pro.ps1` (Advanced users)
- **Best for**: Users comfortable with PowerShell
- **Features**:
  - Enhanced error handling
  - Colored output for better readability
  - Checks for Node.js installation
  - Detailed status messages
- **How to use**: Right-click → "Run with PowerShell"
- **Note**: May require PowerShell execution policy changes

### 3. `Launch Dart Scorer Pro.cmd` (Quick launcher)
- **Best for**: Users who want minimal console interaction
- **Features**:
  - Starts the app quickly
  - Minimizes console window
  - Simple and fast
- **How to use**: Double-click the file

### 4. `Dart Scorer Pro.vbs` (Silent launcher)
- **Best for**: Users who want completely silent operation
- **Features**:
  - No console windows shown
  - Runs completely in background
  - Shows message boxes for important information only
  - Cleanest user experience
- **How to use**: Double-click the file

## 🚀 Quick Start Guide

1. **First Time Setup**:
   - Make sure Node.js is installed on your computer
   - Download from: https://nodejs.org/
   - Choose the LTS (Long Term Support) version

2. **Choose Your Launcher**:
   - For beginners: Use `Dart Scorer Pro.bat`
   - For silent operation: Use `Dart Scorer Pro.vbs`

3. **Run the Application**:
   - Double-click your chosen launcher file
   - On first run, dependencies will be installed automatically
   - The Dart Scorer Pro window will open

## 📋 Prerequisites

- **Node.js**: Must be installed and available in system PATH
- **npm**: Comes with Node.js installation
- **Windows**: Any modern version (Windows 10/11 recommended)

## 🔧 Troubleshooting

### "Node.js not found" Error
- Install Node.js from https://nodejs.org/
- Restart your computer after installation
- Try running the launcher again

### "package.json not found" Error
- Make sure the launcher file is in the same folder as the Dart Scorer Pro files
- The folder should contain: package.json, main.js, index.html, etc.

### PowerShell Execution Policy Error
- Open PowerShell as Administrator
- Run: `Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser`
- Try running the .ps1 file again

### Dependencies Installation Fails
- Check your internet connection
- Try running `npm install` manually in the terminal
- Make sure you have sufficient disk space

## 📁 Installation in Programs Folder

To add Dart Scorer Pro to your Programs folder:

1. **Copy the entire Dart Scorer Pro folder** to one of these locations:
   - `C:\Program Files\Dart Scorer Pro\` (for all users)
   - `C:\Users\<USER>\AppData\Local\Programs\Dart Scorer Pro\` (for current user)

2. **Create a Desktop Shortcut**:
   - Right-click on your chosen launcher file
   - Select "Create shortcut"
   - Move the shortcut to your Desktop

3. **Add to Start Menu** (Optional):
   - Copy the shortcut to: `C:\ProgramData\Microsoft\Windows\Start Menu\Programs\`

## 🎯 Recommended Setup

For the best experience:

1. Use `Dart Scorer Pro.vbs` for daily use (silent operation)
2. Keep `Dart Scorer Pro.bat` as backup for troubleshooting
3. Create a desktop shortcut to your preferred launcher
4. Pin the shortcut to your taskbar for quick access

## 🆘 Support

If you encounter issues:

1. Try the `.bat` launcher first to see detailed error messages
2. Ensure Node.js is properly installed
3. Check that all Dart Scorer Pro files are in the same folder
4. Restart your computer if you just installed Node.js

Enjoy playing darts with Dart Scorer Pro! 🎯
