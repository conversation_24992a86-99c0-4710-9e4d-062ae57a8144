# Dart Scorer Pro - Offline Storage Guide

Dart Scorer Pro now supports **complete offline functionality** with local game storage! You can save and load games without any internet connection or cloud services.

## 🔄 How Offline Storage Works

### Automatic Fallback
- **Firebase Available**: Uses cloud storage for games
- **Firebase Unavailable**: Automatically switches to offline storage
- **No Configuration Required**: The app detects and switches automatically

### Storage Location
- Games are saved in your browser's **localStorage**
- Data persists between app sessions
- Stored locally on your computer/device
- No internet connection required

## 🎯 Offline Storage Features

### ✅ What Works Offline
- **Save Games**: Save your current game progress
- **Load Games**: Load previously saved games
- **Game History**: View list of saved games
- **Export Games**: Download your games as a backup file
- **Import Games**: Restore games from a backup file
- **Full Gameplay**: All dart scoring and game features work offline

### 🔍 Storage Information Display
The app shows your storage status:
- **Orange User ID**: Offline storage mode
- **Green User ID**: Online storage mode  
- **Red User ID**: No storage available

## 💾 Managing Your Offline Games

### Storage Management Panel
Located in the "Load Game" section of the setup screen:

#### Export Games
- **Purpose**: Create a backup of all your saved games
- **File Format**: JSON file with timestamp
- **Usage**: Click "Export Games" → Downloads `dart-scorer-games-YYYY-MM-DD.json`
- **Backup Strategy**: Export regularly to prevent data loss

#### Import Games
- **Purpose**: Restore games from a backup file
- **File Format**: JSON files created by the export function
- **Usage**: Click "Import Games" → Select your backup file
- **Merge Behavior**: Imported games are added to existing games

#### Storage Information
Shows:
- Number of games saved (e.g., "5/20")
- Storage space used in KB
- Your unique offline user ID

## 📋 Step-by-Step Usage

### 1. Starting in Offline Mode
```
1. Launch Dart Scorer Pro
2. Look for orange "Offline - [ID]..." in bottom-left corner
3. Save/Load buttons are automatically enabled
4. Storage Management buttons are available
```

### 2. Saving a Game
```
1. Start or continue a game
2. Click "Save Game" button during gameplay
3. See confirmation: "Game saved offline with ID: [ID]..."
4. Game appears in saved games list
```

### 3. Loading a Game
```
1. Go to setup screen
2. See saved games in "Load Game" section
3. Click on a game ID to load it
4. Game resumes exactly where you left off
```

### 4. Backing Up Your Games
```
1. Go to setup screen
2. Scroll to "Storage Management" section
3. Click "Export Games"
4. Save the downloaded JSON file safely
5. Repeat regularly to maintain backups
```

### 5. Restoring from Backup
```
1. Go to setup screen
2. Click "Import Games" in Storage Management
3. Select your backup JSON file
4. Games are restored and available immediately
```

## 🔧 Technical Details

### Storage Limits
- **Maximum Games**: 20 saved games (vs 10 for online)
- **Automatic Cleanup**: Oldest games deleted when limit exceeded
- **Storage Space**: Limited by browser localStorage (typically 5-10MB)

### Data Format
- **Game State**: Complete game information including:
  - Player names and scores
  - Throw history for each player
  - Game settings (501, 301, etc.)
  - Round information
  - Statistics

### Unique Features
- **Device-Specific**: Each device gets its own offline storage
- **No Account Required**: No login or registration needed
- **Privacy**: All data stays on your device
- **Cross-Session**: Games persist when you close and reopen the app

## 🚨 Important Notes

### Data Persistence
- ✅ **Survives**: App restarts, computer restarts
- ❌ **Lost if**: Browser data cleared, app uninstalled, hard drive failure
- 💡 **Solution**: Regular exports for backup

### Limitations
- **Device-Specific**: Games saved on one device won't appear on another
- **No Sync**: Unlike cloud storage, no automatic synchronization
- **Browser Dependent**: Clearing browser data removes saved games

### Best Practices
1. **Export Regularly**: Create backups weekly or after important games
2. **Safe Storage**: Keep backup files in multiple locations
3. **Test Imports**: Verify your backups work by testing imports
4. **Monitor Storage**: Check storage info to avoid hitting limits

## 🔄 Switching Between Online/Offline

### Automatic Detection
The app automatically chooses the best available storage:

1. **Firebase Available** → Uses online storage
2. **Firebase Unavailable** → Uses offline storage
3. **No Storage Available** → Disables save/load features

### Manual Testing
To force offline mode for testing:
- Remove or rename `firebase-env.js` file
- Restart the application
- App will automatically use offline storage

## 🆘 Troubleshooting

### "No storage system available"
- **Cause**: Both Firebase and localStorage failed
- **Solution**: Check browser settings, ensure localStorage is enabled

### "Storage quota exceeded"
- **Cause**: Too many games saved
- **Solution**: Export games, then delete old ones, or clear all games

### Games disappeared
- **Cause**: Browser data was cleared
- **Solution**: Import from your most recent backup file

### Can't export/import
- **Cause**: Not in offline mode
- **Solution**: These features only work with offline storage

## 🎯 Conclusion

Offline storage makes Dart Scorer Pro completely self-contained and reliable. Whether you're playing in a basement with no internet or just prefer to keep your data local, the offline storage system provides all the functionality you need with the added benefit of complete privacy and control over your game data.

Remember to export your games regularly to maintain backups! 🎯
