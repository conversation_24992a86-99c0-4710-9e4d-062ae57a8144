' Dart Scorer Pro - Silent Launcher
' This VBScript launches the application without showing console windows

Dim objShell, objFSO, strAppDir, strCommand

' Create shell and file system objects
Set objShell = CreateObject("WScript.Shell")
Set objFSO = CreateObject("Scripting.FileSystemObject")

' Get the directory where this script is located
strAppDir = objFSO.GetParentFolderName(WScript.ScriptFullName)

' Change to the application directory
objShell.CurrentDirectory = strAppDir

' Check if package.json exists
If Not objFSO.FileExists(strAppDir & "\package.json") Then
    MsgBox "Error: package.json not found!" & vbCrLf & _
           "Please make sure this script is in the Dart Scorer Pro directory.", _
           vbCritical, "Dart Scorer Pro - Error"
    WScript.Quit 1
End If

' Check if node_modules exists
If Not objFSO.FolderExists(strAppDir & "\node_modules") Then
    ' Show message that dependencies are being installed
    MsgBox "First time setup - Installing dependencies..." & vbCrLf & _
           "This may take a few minutes. Click OK to continue.", _
           vbInformation, "Dart Scorer Pro - Setup"
    
    ' Install dependencies
    strCommand = "cmd /c npm install"
    Dim intResult
    intResult = objShell.Run(strCommand, 1, True)
    
    If intResult <> 0 Then
        MsgBox "Error: Failed to install dependencies!" & vbCrLf & _
               "Please make sure Node.js and npm are installed.", _
               vbCritical, "Dart Scorer Pro - Error"
        WScript.Quit 1
    End If
    
    MsgBox "Dependencies installed successfully!" & vbCrLf & _
           "Starting Dart Scorer Pro...", _
           vbInformation, "Dart Scorer Pro - Setup Complete"
End If

' Launch the application silently
strCommand = "cmd /c npm start"
objShell.Run strCommand, 0, False

' Clean up objects
Set objShell = Nothing
Set objFSO = Nothing
