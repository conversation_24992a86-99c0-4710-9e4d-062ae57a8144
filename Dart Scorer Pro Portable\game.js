// game.js
import * as Player from './player.js';
import * as Dartboard from './dartboard.js';

export function createGame(players, startingScore, mustFinishOnDouble = true) {
    // Ensure players array is fresh for new game, reset scores.
    const gamePlayers = players.map(p => ({
        ...p, // Spread existing player data like name, id
        currentScore: startingScore,
        throwsHistory: [], // History for this specific game/leg
        dartsThisTurn: [],
        stats: Player.resetPlayerStats(p.stats) // Reset stats for the new game
    }));

    return {
        id: `game_${Date.now()}_${Math.random().toString(36).substring(2, 7)}`, // Basic unique ID
        players: gamePlayers,
        settings: {
            startingScore: startingScore,
            mustFinishOnDouble: mustFinishOnDouble,
        },
        currentRound: 1,
        currentPlayerIndex: 0,
        gameLog: [`Game started: ${startingScore} points. ${mustFinishOnDouble ? "Finish on double." : "No double finish required."}`],
        isGameOver: false,
        winner: null,
        turnHistory: [], // To store snapshots for undoing full turns
    };
}

export function rehydrateGame(loadedData, players) {
     // The players array passed in should already be rehydrated Player objects
    return {
        ...loadedData,
        players: players, // Use the rehydrated players
         // Ensure methods or non-serialized parts are handled if any
        // For example, if dartsThisTurn was part of player in save but should be top-level for game state:
        // players: loadedData.players.map(p => ({...p, dartsThisTurn: [] })), // Reset for active turn
    };
}


export function getCurrentPlayer(game) {
    return game.players[game.currentPlayerIndex];
}

export function recordDart(game, score, multiplier, text) {
    const player = getCurrentPlayer(game);
    if (player.dartsThisTurn.length < 3) {
        const dartValue = Dartboard.getDartValue({ score, multiplier });
        player.dartsThisTurn.push({ score, multiplier, text, value: dartValue });
        
        // Update stats for this dart
        player.stats.totalThrows++;
        if (multiplier === 2) player.stats.doublesHit++;
        if (multiplier === 3) player.stats.triplesHit++;
        // game.gameLog.push(`${player.name} threw: ${text} (${dartValue})`); // Optional: log every dart
    }
}

export function undoLastDart(game) {
    const player = getCurrentPlayer(game);
    if (player.dartsThisTurn.length > 0) {
        const undoneDart = player.dartsThisTurn.pop();
        // Revert stats for this dart
        player.stats.totalThrows--;
        if (undoneDart.multiplier === 2) player.stats.doublesHit--;
        if (undoneDart.multiplier === 3) player.stats.triplesHit--;
        // game.gameLog.push(`${player.name} undid last dart: ${undoneDart.text}`);
    }
}

export function calculateTurnScore(dartsThisTurn) {
    return dartsThisTurn.reduce((total, dart) => total + dart.value, 0);
}

export function endTurn(game) {
    const player = getCurrentPlayer(game);
    const turnScore = calculateTurnScore(player.dartsThisTurn);
    const scoreBeforeTurn = player.currentScore;
    let scoreAfterTurn = player.currentScore - turnScore;
    let isBust = false;
    let isWin = false;

    // Bust Logic (X01)
    if (game.settings.mustFinishOnDouble) {
        if (scoreAfterTurn < 0 || scoreAfterTurn === 1) {
            isBust = true;
        } else if (scoreAfterTurn === 0) {
            const lastDart = player.dartsThisTurn[player.dartsThisTurn.length - 1];
            if (lastDart && lastDart.multiplier === 2) {
                isWin = true;
            } else {
                isBust = true; // Must finish on a double
            }
        }
    } else { // No double out rule
        if (scoreAfterTurn < 0) {
            isBust = true;
        } else if (scoreAfterTurn === 0) {
            isWin = true;
        }
    }

    if (isBust) {
        scoreAfterTurn = scoreBeforeTurn; // Score reverts
        game.gameLog.push(`Round ${game.currentRound}: ${player.name} BUSTED! Score remains ${scoreBeforeTurn}. (Threw ${turnScore})`);
    } else {
        player.currentScore = scoreAfterTurn;
        game.gameLog.push(`Round ${game.currentRound}: ${player.name} scored ${turnScore}. (${scoreBeforeTurn} -> ${scoreAfterTurn})`);
        
        // Update player stats for the turn
        player.stats.totalScoreThrown += turnScore;
        if (turnScore > player.stats.highestTurnScore) {
            player.stats.highestTurnScore = turnScore;
        }
        if (turnScore === 180) player.stats.oneEighties++;
    }
    
    // Store turn history for player
    player.throwsHistory.push({
        round: game.currentRound,
        darts: [...player.dartsThisTurn], // Copy of darts
        turnScore: isBust ? 0 : turnScore, // Actual score credited
        rawTurnScore: turnScore, // Score calculated from darts
        scoreBeforeTurn: scoreBeforeTurn,
        scoreAfterTurn: player.currentScore,
        isBust: isBust,
    });

    // Store game state for undo turn
     game.turnHistory.push(JSON.parse(JSON.stringify({ // Deep copy
        players: game.players,
        currentPlayerIndex: game.currentPlayerIndex,
        currentRound: game.currentRound,
        gameLogLength: game.gameLog.length -1 // Index of the log entry for this turn
    })));


    player.dartsThisTurn = []; // Clear darts for next turn

    if (isWin) {
        game.isGameOver = true;
        game.winner = player;
        game.gameLog.push(`${player.name} WINS THE GAME! Final score: 0.`);
    }
    
    return { isWin, isBust, winner: game.winner, turnScore };
}

export function recordBust(game) {
    const player = getCurrentPlayer(game);
    const scoreBeforeTurn = player.currentScore;
    const rawTurnScore = calculateTurnScore(player.dartsThisTurn); // Score if it wasn't a bust

    game.gameLog.push(`Round ${game.currentRound}: ${player.name} declared BUST! Score remains ${scoreBeforeTurn}. (Attempted ${rawTurnScore})`);
    
    player.throwsHistory.push({
        round: game.currentRound,
        darts: [...player.dartsThisTurn],
        turnScore: 0, // No score change for bust
        rawTurnScore: rawTurnScore,
        scoreBeforeTurn: scoreBeforeTurn,
        scoreAfterTurn: scoreBeforeTurn, // Score remains same
        isBust: true,
    });

     game.turnHistory.push(JSON.parse(JSON.stringify({
        players: game.players,
        currentPlayerIndex: game.currentPlayerIndex,
        currentRound: game.currentRound,
        gameLogLength: game.gameLog.length -1
    })));

    player.dartsThisTurn = [];
}


export function moveToNextPlayer(game) {
    if (game.isGameOver) return;

    game.currentPlayerIndex = (game.currentPlayerIndex + 1) % game.players.length;
    if (game.currentPlayerIndex === 0) { // Back to the first player, new round
        game.currentRound++;
    }
    // Ensure the new current player has their dartsThisTurn array ready (should be handled by player object structure)
    if (!getCurrentPlayer(game).dartsThisTurn) {
        getCurrentPlayer(game).dartsThisTurn = [];
    }
}

export function undoLastTurn(game) {
    if (game.turnHistory.length === 0) {
        console.warn("No turns in history to undo.");
        return false;
    }

    const lastTurnState = game.turnHistory.pop();
    
    // Restore player scores and their throwsHistory based on the state *before* the undone turn.
    // The `lastTurnState.players` contains the state *after* the turn was completed.
    // We need to find the player whose turn it was and revert their last `throwsHistory` entry.

    const playerWhoPlayedLast = game.players[lastTurnState.currentPlayerIndex];
    if (playerWhoPlayedLast && playerWhoPlayedLast.throwsHistory.length > 0) {
        const lastThrowEntry = playerWhoPlayedLast.throwsHistory.pop();
        playerWhoPlayedLast.currentScore = lastThrowEntry.scoreBeforeTurn; // Revert score

        // Revert stats from that turn
        if (!lastThrowEntry.isBust) {
            playerWhoPlayedLast.stats.totalScoreThrown -= lastThrowEntry.turnScore;
            // Note: Reverting highestTurnScore, 180s is more complex as it requires knowing previous highs.
            // For simplicity, we might not perfectly revert all stats on undo.
            // Or, we can recalculate stats based on the remaining throwsHistory.
        }
        lastThrowEntry.darts.forEach(dart => {
            playerWhoPlayedLast.stats.totalThrows--;
            if (dart.multiplier === 2) playerWhoPlayedLast.stats.doublesHit--;
            if (dart.multiplier === 3) playerWhoPlayedLast.stats.triplesHit--;
        });
    }


    // Restore game state fields
    game.currentPlayerIndex = lastTurnState.currentPlayerIndex;
    game.currentRound = lastTurnState.currentRound;
    
    // Trim game log to remove the last turn's entry
    if (game.gameLog.length > lastTurnState.gameLogLength) {
       game.gameLog.splice(lastTurnState.gameLogLength);
    }

    game.isGameOver = false; // Game is no longer over if we undo the winning turn
    game.winner = null;
    
    // Reset darts for the current player as their turn is being re-taken
    getCurrentPlayer(game).dartsThisTurn = [];

    console.log("Last turn undone. Current player:", getCurrentPlayer(game).name, "Score:", getCurrentPlayer(game).currentScore);
    return true;
}
