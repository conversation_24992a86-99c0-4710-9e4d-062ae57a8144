Electron Dart Scorer Pro
A desktop application for scoring analog X01 dart games (e.g., 301, 501) played on a traditional cork dartboard.

Features
Player Management: Add, remove, and list active players.

Game Mode Selection:

Choose from common X01 game modes (101, 201, 301, 501, 701).

Option for custom starting scores.

Option to enforce "must finish on a double" rule.

Intuitive Score Entry:

Visual Dartboard: Clickable SVG dartboard segments (Single, Double, Triple, Bull, Double Bull).

Text Input Helper: Enter darts like "T20", "D16", "S5", "Bull", "DB" or just numbers.

The system calculates the turn total from individual dart inputs.

Game Flow & Display:

Clear display of the current round number.

Visually appealing scoreboard showing each player's current score, last turn, and basic stats.

Highlights the current player.

Implements "bust" logic for X01 games.

Detects and announces game winners.

Score Correction & History:

Undo the last dart entered within the current turn.

Undo the entire last completed turn.

Viewable game log of all significant actions.

Advanced Features:

Checkout Helper: Suggests possible checkout combinations for scores up to 170.

Save & Load Games: Persist game state using Firebase Firestore. Load recent games.

Basic Player Statistics: Tracks total throws, average score per turn, highest turn score, doubles/triples hit, and 180s for the current game. Winner stats are displayed.

Toast Notifications: For user feedback on actions.

Technology Choice
Framework: Electron.js

UI: HTML, Tailwind CSS, JavaScript

Backend/Storage: Firebase Firestore (for saving/loading games)

Justification for Electron.js:
Electron was chosen because it allows for the creation of cross-platform (Windows, macOS, Linux) desktop applications using familiar web technologies. This accelerates development, provides robust UI capabilities through HTML/CSS, and allows JavaScript to handle all application logic.

Justification for Score Calculator/Input Helper:
The decision to implement a score calculator where users input individual darts (e.g., "Triple 16", clicking T16 on the board) instead of a total turn score offers several advantages:

Reduces Errors: Minimizes mental arithmetic for the user, reducing the chance of inputting an incorrect total.

Realism: More closely mimics the actual process of playing and scoring darts.

Enables Advanced Features: This input method is crucial for features like a checkout helper and detailed player statistics (e.g., tracking double/triple hit rates).

Granular Data: Allows for detailed data collection about each throw, useful for stats and potential future analysis.

File Structure
electron-dart-scorer/
├── main.js               # Electron main process
├── preload.js            # Electron preload script
├── index.html            # Main UI file
├── renderer.js           # Main UI logic (renderer process)
├── game.js               # Core X01 game logic
├── player.js             # Player management functions
├── ui.js                 # DOM manipulation and UI updates
├── dartboard.js          # Interactive SVG dartboard and dart parsing
├── storage.js            # Firebase Firestore integration for save/load
├── checkout.js           # Checkout suggestion logic
├── package.json          # Project metadata and dependencies
└── README.md             # This file

Setup and Installation
Prerequisites:

Node.js and npm (or yarn) installed. Get it from nodejs.org.

Clone the repository (or download the files):

# If you had a git repo:
# git clone [https://example.com/your-repo-path.git](https://example.com/your-repo-path.git)
# cd electron-dart-scorer

For now, ensure all the provided code files are in a single directory named electron-dart-scorer.

Install Dependencies:
Open a terminal or command prompt in the electron-dart-scorer directory and run:

npm install

This will download Electron and Firebase SDKs.

Firebase Setup (Important for Save/Load Feature):
This application is configured to use Firebase Firestore for saving and loading game data.

The necessary Firebase SDKs are included.

The application uses global variables __firebase_config, __app_id, and __initial_auth_token which are expected to be provided by the hosting environment (like Google's Canvas).

For local development outside such an environment:

You would need to create your own Firebase project at firebase.google.com.

Enable Firestore database in your project.

Get your Firebase project's configuration object (apiKey, authDomain, projectId, etc.).

You would need to modify index.html or renderer.js to use your Firebase config.

In index.html, the window.firebaseEnv object is populated. You'd hardcode your config there for local testing if __firebase_config is not available:

// In index.html, inside the <script type="module"> block:
const firebaseConfigFromGlobal = typeof __firebase_config !== 'undefined' ? __firebase_config : JSON.stringify({
    apiKey: "YOUR_API_KEY",
    authDomain: "YOUR_AUTH_DOMAIN",
    projectId: "YOUR_PROJECT_ID",
    storageBucket: "YOUR_STORAGE_BUCKET",
    messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
    appId: "YOUR_APP_ID"
});
const appIdFromGlobal = typeof __app_id !== 'undefined' ? __app_id : 'your-local-app-id'; // Use a local app ID
const initialAuthTokenFromGlobal = typeof __initial_auth_token !== 'undefined' ? __initial_auth_token : null; // Usually null for anonymous local dev

window.firebaseEnv = {
    firebaseConfig: JSON.parse(firebaseConfigFromGlobal),
    appId: appIdFromGlobal,
    initialAuthToken: initialAuthTokenFromGlobal
};

Ensure your Firestore security rules allow read/write access for authenticated users (even anonymous ones if you wish). A basic rule for development could be:

// rules_version = '2';
// service cloud.firestore {
//   match /databases/{database}/documents {
//     // Allow per-user private data, identified by appId and userId
//     match /artifacts/{appId}/users/{userId}/{document=**} {
//       allow read, write: if request.auth != null && request.auth.uid == userId;
//     }
//   }
// }

Without a valid Firebase setup, the Save/Load game functionality will be disabled or may produce errors. The app will still function for local gameplay.

Running the Application
Once dependencies are installed:

npm start

This command will launch the Electron application.

How to Play
Setup Game:

Add player names one by one.

Select the game mode (e.g., 501, 301) or enter a custom starting score.

Choose if players must finish on a double.

Click "Start Game".

Optionally, you can load a previously saved game by entering its ID. A list of recent saved game IDs will appear if available.

Gameplay:

The current player is highlighted.

Enter scores for each of your three darts:

Click the corresponding segment on the visual dartboard.

Or, type the dart value (e.g., "T20", "D18", "S5", "Bull", "50") into the input field and press Enter or click "Enter Dart".

The scores for the current turn are displayed, along with the turn total.

Undo Dart: Click "Undo Dart" to remove the last dart entered in the current turn.

After throwing up to three darts:

Click "Next Player / Confirm Turn" to submit your score and move to the next player.

If you bust, click "Bust". The system also automatically checks for busts based on X01 rules.

The scoreboard updates with each player's remaining score.

The game log tracks significant events.

Checkout suggestions appear for scores of 170 or less.

Winning:

The first player to reach exactly 0 (on a double, if the rule is enabled) wins.

A winner modal will appear with game stats.

You can then choose to "Play Again" with the same settings or start a "New Game Setup".

Controls:

Save Game: Saves the current game state (requires Firebase setup).

Quit Game: Returns to the setup screen.

Undo Last Turn: Reverts the game state to before the last completed turn.

Future Improvements / Considerations
More sophisticated checkout calculator.

Persistent global player statistics across multiple games/sessions.

Different game modes (e.g., Cricket).

Detailed dart-by-dart statistics (e.g., accuracy on specific numbers).

Customizable UI themes.

AI opponent for practice.

Sound effects.

Packaging the application for distribution (e.g., using electron-builder).