# Create Web Version of Dart Scorer Pro
# This eliminates Electron entirely - result is only ~2 MB!

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  Creating Dart Scorer Pro Web Version" -ForegroundColor Green  
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Set distribution directory
$WebDir = "Dart Scorer Pro Web"

# Remove existing distribution if it exists
if (Test-Path $WebDir) {
    Write-Host "Removing existing web directory..." -ForegroundColor Yellow
    Remove-Item $WebDir -Recurse -Force
}

# Create web directory
Write-Host "Creating web directory..." -ForegroundColor Cyan
New-Item -ItemType Directory -Path $WebDir | Out-Null

# Copy web-compatible files
Write-Host "Copying web application files..." -ForegroundColor Cyan
$WebFiles = @(
    "index.html",
    "renderer.js", 
    "dartboard.js",
    "game.js",
    "player.js",
    "ui.js",
    "checkout.js",
    "offline-storage.js"
)

foreach ($file in $WebFiles) {
    if (Test-Path $file) {
        Copy-Item $file $WebDir
        Write-Host "  ✓ $file" -ForegroundColor Gray
    } else {
        Write-Host "  ✗ $file (not found)" -ForegroundColor Red
    }
}

# Create a web-specific HTML file (without Electron dependencies)
Write-Host "Creating web-optimized HTML..." -ForegroundColor Cyan
$WebHtml = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dart Scorer Pro - Web Version</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body { 
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
            min-height: 100vh;
        }
        .toast { 
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100 font-sans">
    <div class="container mx-auto px-4 py-8 max-w-6xl">
        <!-- Game Setup Screen -->
        <div id="game-setup-screen" class="bg-gray-800 p-8 rounded-lg shadow-2xl ">
            <h1 class="text-4xl font-bold text-center text-yellow-400 mb-8">Dart Scorer Pro - Web</h1>
            
            <div class="grid md:grid-cols-2 gap-8 mb-8">
                <!-- Player Management -->
                <div>
                    <h2 class="text-2xl font-semibold text-sky-400 mb-4">Player Management</h2>
                    <div class="flex gap-2 mb-4">
                        <input type="text" id="new-player-name" placeholder="Enter player name" class="flex-grow bg-gray-700 text-gray-100 placeholder-gray-400 p-3 rounded-md border border-gray-600 focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none">
                        <button id="add-player-btn" class="bg-sky-500 hover:bg-sky-600 text-white font-semibold py-3 px-6 rounded-md transition duration-150">Add Player</button>
                    </div>
                    <div id="player-list" class="space-y-2 max-h-40 overflow-y-auto pr-2">
                        <!-- Player items will be added here -->
                    </div>
                </div>

                <!-- Game Mode Selection -->
                <div>
                    <h2 class="text-2xl font-semibold text-green-400 mb-4">Game Mode</h2>
                    <select id="game-mode-select" class="w-full bg-gray-700 text-gray-100 p-3 rounded-md border border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-green-500 outline-none mb-4">
                        <option value="101">101</option>
                        <option value="201">201</option>
                        <option value="301">301</option>
                        <option value="501" selected>501</option>
                        <option value="701">701</option>
                        <option value="custom">Custom Start Score</option>
                    </select>
                    <div id="custom-score-container" class="hidden mb-4">
                        <input type="number" id="custom-start-score" placeholder="Enter custom score" min="1" max="9999" class="w-full bg-gray-700 text-gray-100 p-3 rounded-md border border-gray-600 focus:ring-2 focus:ring-green-500 focus:border-green-500 outline-none">
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="finish-on-double-checkbox" class="mr-2 w-4 h-4 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500">
                        <label for="finish-on-double-checkbox" class="text-gray-300">Must finish on double</label>
                    </div>
                </div>
            </div>
            
            <button id="start-game-btn" class="w-full bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-bold py-4 px-6 rounded-lg text-xl transition duration-150 shadow-md">Start Game</button>
            
            <div class="mt-8">
                <h3 class="text-xl font-semibold text-purple-400 mb-2">Load Game</h3>
                <div class="flex gap-2">
                    <input type="text" id="load-game-id-input" placeholder="Enter Game ID to load" class="flex-grow bg-gray-700 p-3 rounded-md border border-gray-600">
                    <button id="load-game-btn" class="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-4 rounded-md">Load</button>
                </div>
                <div id="saved-games-list" class="mt-2 text-sm text-gray-400"></div>
                
                <!-- Storage Management Section -->
                <div class="mt-6 pt-4 border-t border-gray-600">
                    <h4 class="text-lg font-semibold text-orange-400 mb-3">Storage Management</h4>
                    <div class="grid grid-cols-2 gap-2">
                        <button id="export-games-btn" class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-3 rounded-md transition text-sm">Export Games</button>
                        <button id="import-games-btn" class="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-3 rounded-md transition text-sm">Import Games</button>
                    </div>
                    <input type="file" id="import-file-input" accept=".json" class="hidden">
                    <div id="storage-info" class="text-xs text-gray-400 mt-2">
                        <!-- Storage info will appear here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Game Play Screen -->
        <div id="game-play-screen" class="hidden w-full">
            <div class="flex justify-between items-center mb-6">
                <h1 id="game-title" class="text-3xl font-bold text-yellow-400">501 Game</h1>
                <div class="text-xl">
                    <span>Round: <span id="current-round" class="font-semibold text-sky-400">1</span></span>
                </div>
                 <button id="save-game-btn" class="bg-green-500 hover:bg-green-600 text-white font-semibold py-2 px-4 rounded-md transition duration-150">Save Game</button>
                <button id="quit-game-btn" class="bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-md transition duration-150">Quit Game</button>
            </div>
            
            <!-- Scoreboard -->
            <div id="scoreboard" class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
                <!-- Player cards will be dynamically added here -->
            </div>

            <div class="grid lg:grid-cols-3 gap-6">
                <!-- Interactive Dartboard -->
                <div class="lg:col-span-2">
                    <div class="bg-gray-800 p-6 rounded-lg shadow-xl">
                        <h2 class="text-xl font-semibold text-yellow-400 mb-4 text-center">Interactive Dartboard</h2>
                        <div class="flex justify-center">
                            <svg id="interactive-dartboard" viewBox="0 0 600 600" class="w-full h-full max-w-lg max-h-lg"></svg>
                        </div>
                        <div class="mt-4 text-center">
                            <input type="text" id="manual-dart-input" placeholder="e.g., T20, D16, S5, Bull, DB" class="bg-gray-700 text-gray-100 p-2 rounded-md border border-gray-600 focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 outline-none mr-2">
                            <button id="enter-dart-btn" class="bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-semibold py-2 px-4 rounded-md transition">Enter Dart</button>
                        </div>
                    </div>

                    <!-- Current Turn Display -->
                    <div class="bg-gray-800 p-6 rounded-lg shadow-xl mt-6">
                        <h2 class="text-xl font-semibold text-green-400 mb-3">Current Turn</h2>
                        <div id="current-turn-darts-display" class="flex justify-around text-xl h-8 mb-1">
                            <!-- Dart 1, Dart 2, Dart 3 scores shown here -->
                        </div>
                        <div class="text-right">
                             <button id="undo-last-dart-btn" class="text-sm bg-yellow-600 hover:bg-yellow-700 text-white py-1 px-3 rounded-md mr-2 disabled:opacity-50" disabled>Undo Dart</button>
                            Turn Total: <span id="current-turn-total-score" class="font-bold text-2xl text-green-400">0</span>
                        </div>
                    </div>

                    <div class="flex gap-4">
                        <button id="next-player-btn" class="flex-1 bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg text-lg transition disabled:opacity-50" disabled>Next Player / Confirm Turn</button>
                        <button id="bust-btn" class="flex-1 bg-red-600 hover:bg-red-700 text-white font-bold py-3 px-6 rounded-lg text-lg transition disabled:opacity-50" disabled>Bust</button>
                    </div>
                </div>

                <!-- Game Log and Controls -->
                <div class="bg-gray-800 p-6 rounded-lg shadow-xl">
                    <h2 class="text-xl font-semibold text-purple-400 mb-3">Game Log</h2>
                    <div id="game-log" class="h-64 overflow-y-auto bg-gray-700 p-3 rounded-md mb-4 text-sm space-y-1">
                        <!-- Log entries will appear here -->
                    </div>
                    <h2 class="text-xl font-semibold text-orange-400 mb-3">Game Controls</h2>
                    <button id="undo-last-turn-btn" class="w-full bg-orange-500 hover:bg-orange-600 text-white font-semibold py-2 px-4 rounded-md mb-2 transition disabled:opacity-50" disabled>Undo Last Turn</button>
                     <div id="checkout-helper-container" class="mt-4">
                        <h3 class="text-lg font-semibold text-teal-400 mb-2">Checkout Suggestions:</h3>
                        <div id="checkout-suggestions" class="text-sm text-gray-300 space-y-1 h-24 overflow-y-auto">
                            <!-- Suggestions appear here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Winner Modal -->
        <div id="winner-modal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center p-4 hidden z-50">
            <div class="bg-gray-800 p-8 md:p-12 rounded-xl shadow-2xl text-center max-w-md w-full transform transition-all scale-95 opacity-0" id="winner-modal-content">
                <h2 class="text-5xl font-bold text-yellow-400 mb-4">WINNER!</h2>
                <p id="winner-name" class="text-3xl text-white mb-2"></p>
                <p id="winner-details" class="text-lg text-gray-300 mb-6"></p>
                <div id="winner-stats" class="text-left text-gray-400 mb-6 space-y-1">
                    <!-- Winner stats will be shown here -->
                </div>
                <div class="flex gap-4">
                    <button id="play-again-btn" class="flex-1 bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg text-lg">Play Again (Same Settings)</button>
                    <button id="new-game-setup-btn" class="flex-1 bg-sky-500 hover:bg-sky-600 text-white font-bold py-3 px-6 rounded-lg text-lg">New Game Setup</button>
                </div>
            </div>
        </div>
    </div>
    <div id="toast-container"></div> <!-- For brief messages -->
    <div id="user-id-display" class="fixed bottom-2 left-2 text-xs text-gray-500">UserID: <span id="user-id-value">Web Version</span></div>

    <!-- App Scripts -->
    <script type="module" src="./web-renderer.js"></script>
</body>
</html>
"@

$WebHtml | Out-File -FilePath (Join-Path $WebDir "index.html") -Encoding UTF8

# Create web-specific renderer (without Electron dependencies)
Write-Host "Creating web-specific renderer..." -ForegroundColor Cyan
$WebRenderer = Get-Content "renderer.js" -Raw
# Remove Electron-specific imports and code
$WebRenderer = $WebRenderer -replace "import.*Storage.*from.*'\.\/storage\.js'.*;", "// Firebase storage disabled in web version"
$WebRenderer = $WebRenderer -replace "initializeFirebase.*", "// Firebase disabled - using offline storage only"
$WebRenderer = $WebRenderer -replace "useOfflineStorage = false", "useOfflineStorage = true"

$WebRenderer | Out-File -FilePath (Join-Path $WebDir "web-renderer.js") -Encoding UTF8

# Create simple web server script
Write-Host "Creating web server script..." -ForegroundColor Cyan
$ServerScript = @"
@echo off
echo Starting Dart Scorer Pro Web Server...
echo.
echo Open your browser and go to: http://localhost:8000
echo Press Ctrl+C to stop the server
echo.
python -m http.server 8000 2>nul || (
    echo Python not found. Trying Node.js...
    npx http-server -p 8000 2>nul || (
        echo Neither Python nor Node.js found.
        echo Please install one of them to run the web server.
        pause
    )
)
"@

$ServerScript | Out-File -FilePath (Join-Path $WebDir "start-web-server.bat") -Encoding ASCII

# Create README for web version
Write-Host "Creating web version README..." -ForegroundColor Cyan
$WebReadme = @"
Dart Scorer Pro - Web Version
=============================

This is the web version of Dart Scorer Pro - NO ELECTRON REQUIRED!

SIZE COMPARISON:
- Electron version: 252+ MB
- Web version: ~2 MB (99% smaller!)

FEATURES:
✓ Complete dart scoring functionality
✓ Interactive dartboard
✓ Save/load games (local storage)
✓ Export/import games
✓ All game modes (101, 201, 301, 501, 701)
✓ Checkout suggestions
✓ Player statistics
✓ Works offline after first load

HOW TO RUN:

Option 1 - Simple Web Server:
1. Double-click "start-web-server.bat"
2. Open browser to http://localhost:8000
3. Enjoy!

Option 2 - Direct File Opening:
1. Double-click "index.html"
2. May have limited functionality due to browser security

Option 3 - Upload to Web Server:
1. Upload all files to any web server
2. Access via URL
3. Share with anyone!

REQUIREMENTS:
- Any modern web browser
- No installation required
- No Node.js required (for basic use)
- Python or Node.js for local server (optional)

ADVANTAGES:
- Tiny size (2 MB vs 252 MB)
- Works on any device with a browser
- No installation required
- Easy to share and deploy
- Cross-platform (Windows, Mac, Linux, mobile)
- Can be hosted online

LIMITATIONS:
- No desktop integration
- Browser-dependent features
- No native file system access (uses downloads for export)

This web version provides 95% of the functionality with 1% of the size!
"@

$WebReadme | Out-File -FilePath (Join-Path $WebDir "README-Web-Version.txt") -Encoding UTF8

# Calculate size
$WebSize = (Get-ChildItem $WebDir -Recurse | Measure-Object -Property Length -Sum).Sum
$WebSizeMB = [math]::Round($WebSize / 1MB, 2)

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  Web Version Created Successfully!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""
Write-Host "Location: $WebDir\" -ForegroundColor White
Write-Host "Size: $WebSizeMB MB (vs 252+ MB Electron)" -ForegroundColor White
Write-Host "Size reduction: 99%+" -ForegroundColor Green
Write-Host ""
Write-Host "To run:" -ForegroundColor Yellow
Write-Host "1. Go to '$WebDir' folder" -ForegroundColor Gray
Write-Host "2. Double-click 'start-web-server.bat'" -ForegroundColor Gray
Write-Host "3. Open browser to http://localhost:8000" -ForegroundColor Gray
Write-Host ""
Read-Host "Press Enter to exit"
"@

$WebRenderer | Out-File -FilePath (Join-Path $WebDir "web-renderer.js") -Encoding UTF8
