// storage.js
import { doc, setDoc, getDoc, collection, addDoc, getDocs, query, where, orderBy, limit, serverTimestamp, deleteDoc } from "https://www.gstatic.com/firebasejs/10.12.2/firebase-firestore.js";

let dbInstance = null;
let userId = null;
let appId = 'default-dart-app'; // Default, will be overwritten by firebaseEnv

const MAX_SAVED_GAMES = 10; // Limit the number of saved games displayed/kept per user

export function setDb(db) {
    dbInstance = db;
}
export function setUserId(uid) {
    userId = uid;
}
export function setAppId(currentAppId) {
    appId = currentAppId;
}

function getUserGamesCollectionPath() {
    if (!userId) throw new Error("User ID not set for Firestore operation.");
    // Using private user data path
    return `artifacts/${appId}/users/${userId}/dartGames`;
}


// --- Game Save/Load ---
export async function saveGame(gameState) {
    if (!dbInstance || !userId) {
        console.error("Firestore not initialized or user not authenticated.");
        throw new Error("Save failed: Connection issue.");
    }

    const gameToSave = {
        ...gameState,
        players: gameState.players.map(p => ({ // Sanitize/prepare player data for saving
            id: p.id,
            name: p.name,
            currentScore: p.currentScore,
            throwsHistory: p.throwsHistory || [],
            stats: p.stats || {} 
        })),
        savedAt: serverTimestamp(), // Add a timestamp
        userId: userId // Associate game with user
    };
    
    // If game has an ID (meaning it was loaded or previously saved), update it. Otherwise, create new.
    const gameId = gameState.id || `game_${Date.now()}`;
    const gameRef = doc(dbInstance, getUserGamesCollectionPath(), gameId);
    
    try {
        await setDoc(gameRef, gameToSave, { merge: true }); // Use setDoc with merge to update or create
        console.log("Game saved successfully with ID:", gameId);
        return gameId;
    } catch (error) {
        console.error("Error saving game to Firestore:", error);
        throw error;
    }
}

export async function loadGame(gameId) {
    if (!dbInstance || !userId) {
        console.error("Firestore not initialized or user not authenticated for load.");
        return null;
    }
    const gameRef = doc(dbInstance, getUserGamesCollectionPath(), gameId);
    try {
        const docSnap = await getDoc(gameRef);
        if (docSnap.exists()) {
            console.log("Game loaded successfully:", docSnap.data());
            return docSnap.data(); // This is the raw game state
        } else {
            console.warn("No such game document found with ID:", gameId);
            return null;
        }
    } catch (error) {
        console.error("Error loading game from Firestore:", error);
        throw error;
    }
}

export async function listSavedGames(callback) {
    if (!dbInstance || !userId) {
        console.warn("Cannot list saved games: Firestore not ready or no user.");
        if (callback) callback([]);
        return;
    }
    try {
        const gamesCollectionRef = collection(dbInstance, getUserGamesCollectionPath());
        // Order by 'savedAt' descending and limit results
        const q = query(gamesCollectionRef, orderBy("savedAt", "desc"), limit(MAX_SAVED_GAMES));
        
        const querySnapshot = await getDocs(q);
        const gameIds = [];
        querySnapshot.forEach((doc) => {
            gameIds.push(doc.id); // Store the document ID which is our gameId
        });
        
        if (callback) callback(gameIds);
        else return gameIds;

    } catch (error) {
        console.error("Error listing saved games:", error);
        if (callback) callback([]); // Return empty on error
        else throw error;
    }
}


export async function deleteOldestGamesIfExceedsLimit() {
    // This is an optional maintenance function you might call periodically
    if (!dbInstance || !userId) return;

    try {
        const gamesCollectionRef = collection(dbInstance, getUserGamesCollectionPath());
        const qCount = query(gamesCollectionRef);
        const countSnapshot = await getDocs(qCount);

        if (countSnapshot.size > MAX_SAVED_GAMES) {
            const numToDelete = countSnapshot.size - MAX_SAVED_GAMES;
            // Query for the oldest games to delete (ascending order, limit to numToDelete)
            const qDelete = query(gamesCollectionRef, orderBy("savedAt", "asc"), limit(numToDelete));
            const deleteSnapshot = await getDocs(qDelete);
            
            const deletePromises = [];
            deleteSnapshot.forEach((doc) => {
                console.log(`Deleting old game: ${doc.id}`);
                deletePromises.push(deleteDoc(doc.ref));
            });
            await Promise.all(deletePromises);
            console.log(`${numToDelete} oldest game(s) deleted.`);
        }
    } catch (error) {
        console.error("Error deleting old games:", error);
    }
}


// --- Player Stats (Example - could be expanded) ---
// This is a more complex feature. For now, stats are part of the game state.
// If you want global, persistent player stats across all games:
// 1. Define a new Firestore collection, e.g., `artifacts/${appId}/users/${userId}/playerProfiles`
// 2. Each document could be a player's profile, identified by player.id or player.name (if unique)
// 3. Update these profiles after each game.

export async function updateGlobalPlayerStats(player) {
    if (!dbInstance || !userId || !player || !player.id) {
        // console.warn("Cannot update global player stats: missing data or Firestore connection.");
        return;
    }
    // This is a placeholder for a more robust global stats system.
    // For now, stats are saved within each game document.
    // If you implement global stats, you'd fetch a player's global profile,
    // update it (e.g., increment gamesWon, update averages), and save it back.
    // Example path: `artifacts/${appId}/users/${userId}/playerProfiles/${player.id}`
    console.log(`Placeholder: Would update global stats for player ${player.name} (ID: ${player.id}) here.`);
}
