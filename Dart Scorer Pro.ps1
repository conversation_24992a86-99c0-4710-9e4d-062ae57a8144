# Dart Scorer Pro - PowerShell Launcher
# This PowerShell script launches the Dart Scorer Pro Electron application

# Set window title
$Host.UI.RawUI.WindowTitle = "Dart Scorer Pro - Launcher"

# Get the directory where this script is located
$AppDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Change to the application directory
Set-Location $AppDir

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "  Dart Scorer Pro - Windows Launcher" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check if Node.js is installed
try {
    $nodeVersion = node --version 2>$null
    if ($nodeVersion) {
        Write-Host "Node.js version: $nodeVersion" -ForegroundColor Cyan
    } else {
        throw "Node.js not found"
    }
} catch {
    Write-Host "ERROR: Node.js is not installed or not in PATH!" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if npm is available
try {
    $npmVersion = npm --version 2>$null
    if ($npmVersion) {
        Write-Host "npm version: $npmVersion" -ForegroundColor Cyan
    } else {
        throw "npm not found"
    }
} catch {
    Write-Host "ERROR: npm is not available!" -ForegroundColor Red
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if package.json exists
if (-not (Test-Path "package.json")) {
    Write-Host "ERROR: package.json not found!" -ForegroundColor Red
    Write-Host "Please make sure this script is in the Dart Scorer Pro directory." -ForegroundColor Yellow
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Check if node_modules exists, install if not
if (-not (Test-Path "node_modules")) {
    Write-Host ""
    Write-Host "First time setup - Installing dependencies..." -ForegroundColor Yellow
    Write-Host "This may take a few minutes..." -ForegroundColor Yellow
    Write-Host ""
    
    try {
        & npm install
        if ($LASTEXITCODE -ne 0) {
            throw "npm install failed"
        }
        Write-Host ""
        Write-Host "Dependencies installed successfully!" -ForegroundColor Green
    } catch {
        Write-Host ""
        Write-Host "ERROR: Failed to install dependencies!" -ForegroundColor Red
        Write-Host "Error: $_" -ForegroundColor Red
        Write-Host ""
        Read-Host "Press Enter to exit"
        exit 1
    }
}

# Start the application
Write-Host ""
Write-Host "Starting Dart Scorer Pro..." -ForegroundColor Green
Write-Host "The application window should open shortly." -ForegroundColor Cyan
Write-Host ""
Write-Host "To close the application, close the Dart Scorer Pro window" -ForegroundColor Yellow
Write-Host "or press Ctrl+C in this console." -ForegroundColor Yellow
Write-Host ""

try {
    # Launch the Electron application
    & npm start
} catch {
    Write-Host ""
    Write-Host "ERROR: Failed to start the application!" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    Write-Host ""
    Read-Host "Press Enter to exit"
    exit 1
}

# Application has closed
Write-Host ""
Write-Host "Dart Scorer Pro has closed." -ForegroundColor Cyan
Write-Host ""
Read-Host "Press Enter to exit"
