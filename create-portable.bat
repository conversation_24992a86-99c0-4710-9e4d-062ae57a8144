@echo off
REM Create Portable Distribution of Dart Scorer Pro
REM This script creates a minimal portable version without the full node_modules

echo ========================================
echo  Creating Dart Scorer Pro Portable
echo ========================================
echo.

REM Create distribution directory
set "DIST_DIR=Dart Scorer Pro Portable"
if exist "%DIST_DIR%" (
    echo Removing existing distribution directory...
    rmdir /s /q "%DIST_DIR%"
)

echo Creating distribution directory...
mkdir "%DIST_DIR%"

REM Copy essential application files
echo Copying application files...
copy "main.js" "%DIST_DIR%\" >nul
copy "renderer.js" "%DIST_DIR%\" >nul
copy "index.html" "%DIST_DIR%\" >nul
copy "package.json" "%DIST_DIR%\" >nul

REM Copy all JavaScript modules
echo Copying JavaScript modules...
copy "*.js" "%DIST_DIR%\" >nul

REM Copy launcher files
echo Copying launcher files...
copy "Dart Scorer Pro.bat" "%DIST_DIR%\" >nul 2>nul
copy "Dart Scorer Pro.vbs" "%DIST_DIR%\" >nul 2>nul
copy "Launch Dart Scorer Pro.cmd" "%DIST_DIR%\" >nul 2>nul

REM Copy documentation
echo Copying documentation...
copy "*.md" "%DIST_DIR%\" >nul 2>nul

REM Create minimal node_modules with only Electron
echo Creating minimal node_modules...
mkdir "%DIST_DIR%\node_modules"

REM Check if electron exists and copy it
if exist "node_modules\electron" (
    echo Copying Electron runtime...
    xcopy "node_modules\electron" "%DIST_DIR%\node_modules\electron" /e /i /q >nul
) else (
    echo WARNING: Electron not found in node_modules
    echo Please run 'npm install' first
    pause
    exit /b 1
)

REM Create a simple README for the portable version
echo Creating portable README...
echo Dart Scorer Pro - Portable Version > "%DIST_DIR%\README-Portable.txt"
echo. >> "%DIST_DIR%\README-Portable.txt"
echo This is a portable version of Dart Scorer Pro. >> "%DIST_DIR%\README-Portable.txt"
echo. >> "%DIST_DIR%\README-Portable.txt"
echo To run: >> "%DIST_DIR%\README-Portable.txt"
echo 1. Double-click "Dart Scorer Pro.bat" (shows console) >> "%DIST_DIR%\README-Portable.txt"
echo 2. Double-click "Dart Scorer Pro.vbs" (silent mode) >> "%DIST_DIR%\README-Portable.txt"
echo 3. Double-click "Launch Dart Scorer Pro.cmd" (minimal) >> "%DIST_DIR%\README-Portable.txt"
echo. >> "%DIST_DIR%\README-Portable.txt"
echo Requirements: >> "%DIST_DIR%\README-Portable.txt"
echo - Node.js must be installed on the target computer >> "%DIST_DIR%\README-Portable.txt"
echo - Download from: https://nodejs.org/ >> "%DIST_DIR%\README-Portable.txt"

REM Calculate and display size information
echo.
echo ========================================
echo  Portable Version Created Successfully!
echo ========================================
echo.
echo Location: %DIST_DIR%\
echo.

REM Try to get folder size (Windows 10/11)
for /f "tokens=3" %%a in ('dir "%DIST_DIR%" /-c ^| find "File(s)"') do set size=%%a
if defined size (
    echo Approximate size: %size% bytes
) else (
    echo Size: Check folder properties for exact size
)

echo.
echo You can now:
echo 1. Copy the "%DIST_DIR%" folder to any computer with Node.js
echo 2. Run any of the launcher files to start the application
echo 3. Zip the folder for easy distribution
echo.
echo Note: This portable version is much smaller than the full
echo development folder because it excludes unnecessary files.
echo.
pause
